// src/components/LoanProgressTracker.tsx
"use client";

import {
  Box,
  Flex,
  Text,
  VStack,
  HStack,
  Image as ChakraImage,
  CircularProgress,
  CircularProgressLabel,
} from "@chakra-ui/react";
import { motion, AnimatePresence } from "framer-motion";
import { useMemo, memo } from "react";

import { FlexProps } from "@chakra-ui/react";

// Create motion components
const MotionBox = motion(Box);
const MotionText = motion(Text);
const MotionHStack = motion(HStack);

// Utility function to generate consistent colors based on name
const generateAvatarColor = (name: string): string => {
  const colors = [
    "linear(to-br, #667eea, #764ba2)",
    "linear(to-br, #f093fb, #f5576c)",
    "linear(to-br, #4facfe, #00f2fe)",
    "linear(to-br, #43e97b, #38f9d7)",
    "linear(to-br, #fa709a, #fee140)",
    "linear(to-br, #a8edea, #fed6e3)",
    "linear(to-br, #ff9a9e, #fecfef)",
    "linear(to-br, #ffecd2, #fcb69f)",
    "linear(to-br, #a18cd1, #fbc2eb)",
    "linear(to-br, #fad0c4, #ffd1ff)",
  ];

  // Generate a consistent index based on the name
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

// Optimized animation variants with reduced motion for performance
const fadeInUp = {
  initial: { opacity: 0, y: 8 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -8 },
};

const scaleIn = {
  initial: { opacity: 0, scale: 0.9 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.9 },
};

// Optimized transition settings
const fastTransition = { duration: 0.2, ease: "easeOut" };
const mediumTransition = { duration: 0.3, ease: "easeOut" };

interface LoanProgressTrackerProps extends FlexProps {
  progress: number;
  passportPhotoUrl?: string;
  userEmail?: string;
  userName?: string;
}

const LoanProgressTracker = memo(function LoanProgressTracker({
  progress,
  passportPhotoUrl,
  userEmail,
  userName,
  ...rest
}: LoanProgressTrackerProps) {
  // Memoize computed values for performance
  const avatarData = useMemo(() => {
    if (!userName) return null;

    const firstLetter = userName.charAt(0).toUpperCase();
    const bgGradient = generateAvatarColor(userName);

    return { firstLetter, bgGradient };
  }, [userName]);

  const hasUserData = Boolean(userName || userEmail);
  const showPassportPhoto = Boolean(passportPhotoUrl);
  const showInitialAvatar = Boolean(userName && !passportPhotoUrl);
  return (
    <Flex
      width={{ base: "100%", lg: "24%" }}
      flexDirection="column"
      p={6}
      mt={{ base: 6, lg: 14 }}
      borderRadius="md"
      boxShadow="md"
      height="fit-content"
      {...rest}
    >
      <Text
        fontSize="xl"
        fontWeight="semibold"
        align="center"
        color="gray.900"
        mb={4}
      >
        Loan Progress Tracker
      </Text>
      <VStack
        spacing={4}
        borderBottom="2px solid #F5F5F5"
        pb={6}
        align="center"
      >
        <HStack justifyContent="space-between">
          {/* <Text>Personal Information</Text> */}
          {/* <Text>{progress.toFixed(0)}%</Text> */}
        </HStack>
        <Box
          position="relative"
          w={{ base: "180px", md: "240px" }}
          h={{ base: "180px", md: "240px" }}
        >
          <CircularProgress
            value={progress}
            color="#3461ff"
            size="100%"
            capIsRound
          >
            <CircularProgressLabel fontSize={20}>
              {progress.toFixed(0)}%
            </CircularProgressLabel>
          </CircularProgress>
        </Box>
      </VStack>
      <AnimatePresence mode="wait">
        {hasUserData ? (
          <MotionHStack
            key="user-info"
            mt={6}
            px={4}
            py={2}
            bgColor="gray.50"
            rounded="2xl"
            variants={fadeInUp}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={mediumTransition}
          >
            {/* Avatar Section */}
            <Box position="relative" w="48px" h="48px" mr={2}>
              <AnimatePresence mode="wait">
                {showPassportPhoto ? (
                  <MotionBox
                    key="passport-photo"
                    variants={scaleIn}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    transition={mediumTransition}
                    w="100%"
                    h="100%"
                  >
                    <ChakraImage
                      src={passportPhotoUrl}
                      alt="Passport Photo"
                      w="48px"
                      h="48px"
                      borderRadius="full"
                      objectFit="cover"
                      loading="lazy"
                    />
                  </MotionBox>
                ) : showInitialAvatar && avatarData ? (
                  <MotionBox
                    key="initial-avatar"
                    variants={scaleIn}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    transition={fastTransition}
                    w="48px"
                    h="48px"
                    borderRadius="full"
                    bgGradient={avatarData.bgGradient}
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    color="white"
                    fontSize="20px"
                    fontWeight="bold"
                    textShadow="0 1px 2px rgba(0,0,0,0.1)"
                  >
                    {avatarData.firstLetter}
                  </MotionBox>
                ) : (
                  <MotionBox
                    key="default-icon"
                    variants={scaleIn}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    transition={fastTransition}
                    w="100%"
                    h="100%"
                  >
                    <ChakraImage
                      src="/loan-icon.png"
                      alt="Loan Icon"
                      w="48px"
                      h="48px"
                    />
                  </MotionBox>
                )}
              </AnimatePresence>
            </Box>

            {/* User Info Section */}
            <VStack align="stretch" spacing={0} flex={1}>
              <AnimatePresence mode="wait">
                {userName ? (
                  <MotionText
                    key={`name-${userName}`}
                    fontSize={16}
                    fontWeight="semibold"
                    variants={fadeInUp}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    transition={{ ...fastTransition, delay: 0.05 }}
                  >
                    {userName}
                  </MotionText>
                ) : (
                  <Text fontSize={16} fontWeight="semibold" opacity={0.5}>
                    Enter your name
                  </Text>
                )}
              </AnimatePresence>

              <AnimatePresence mode="wait">
                {userEmail ? (
                  <MotionText
                    key={`email-${userEmail}`}
                    fontSize={14}
                    color="gray.500"
                    variants={fadeInUp}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    transition={{ ...fastTransition, delay: 0.1 }}
                  >
                    {userEmail}
                  </MotionText>
                ) : (
                  <Text fontSize={14} color="gray.400" opacity={0.5}>
                    Enter your email
                  </Text>
                )}
              </AnimatePresence>
            </VStack>
          </MotionHStack>
        ) : (
          <MotionHStack
            key="placeholder"
            mt={6}
            px={4}
            py={2}
            bgColor="gray.50"
            rounded="2xl"
            variants={fadeInUp}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={mediumTransition}
          >
            <ChakraImage
              src="/loan-icon.png"
              alt="Loan Icon"
              w="48px"
              h="48px"
              mr={2}
              opacity={0.6}
            />
            <VStack align="stretch" spacing={0}>
              <Text fontSize={16} fontWeight="semibold" color="gray.400">
                Enter your details
              </Text>
              <Text fontSize={14} color="gray.400">
                to see your progress
              </Text>
            </VStack>
          </MotionHStack>
        )}
      </AnimatePresence>
    </Flex>
  );
});

export default LoanProgressTracker;
