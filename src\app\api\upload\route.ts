import { put } from "@vercel/blob";
import { NextResponse } from "next/server";

export async function POST(request: Request): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const filename = searchParams.get("filename");
    const fileType = searchParams.get("fileType");

    // ⚠️ The below code is for App Router Route Handlers only
    if (!process.env.BLOB_READ_WRITE_TOKEN) {
      return new NextResponse(
        JSON.stringify({ message: "Missing BLOB_READ_WRITE_TOKEN" }),
        {
          status: 401,
        }
      );
    }

    if (!filename || !request.body) {
      return NextResponse.json(
        { message: "No file to upload." },
        { status: 400 }
      );
    }

    // Validate file type on backend
    const allowedTypes = ["application/pdf", "image/png"];
    if (fileType && !allowedTypes.includes(fileType)) {
      return NextResponse.json(
        { message: "Invalid file type. Only PDF and PNG files are allowed." },
        { status: 400 }
      );
    }

    // Validate file extension
    const fileExtension = filename.toLowerCase().split(".").pop();
    const allowedExtensions = ["pdf", "png"];
    if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
      return NextResponse.json(
        {
          message:
            "Invalid file extension. Only .pdf and .png files are allowed.",
        },
        { status: 400 }
      );
    }

    const blob = await put(filename, request.body, {
      access: "public",
      addRandomSuffix: true,
      allowOverwrite: false,
      token: process.env.BLOB_READ_WRITE_TOKEN,
    });

    return NextResponse.json(blob);
  } catch (error) {
    console.error("File upload error:", error);
    return NextResponse.json(
      {
        message: "File upload failed. Please try again.",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
